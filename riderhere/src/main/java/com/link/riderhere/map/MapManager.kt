package com.link.riderhere.map

import android.content.Context
import android.util.Log
import com.here.sdk.core.GeoCoordinates
import com.here.sdk.core.Location
import com.here.sdk.core.errors.InstantiationErrorException
import com.here.sdk.mapview.MapView
import com.here.sdk.routing.CalculateRouteCallback
import com.here.sdk.routing.Route
import com.here.sdk.routing.RoutingEngine
import com.here.sdk.routing.RoutingError
import com.here.sdk.routing.ScooterOptions
import com.here.sdk.routing.Waypoint
import com.here.sdk.search.Place
import com.link.riderhere.api.RiderMap
import com.link.riderhere.api.dto.NavigationInfo
import com.link.riderhere.api.dto.SearchResult
import com.link.riderhere.map.location.data.repository.LocationRepositoryImpl
import com.link.riderhere.map.location.data.source.HereLocationDataSourceImpl
import com.link.riderhere.map.location.data.source.HereLocationListener
import com.link.riderhere.map.navi.LocalNavigation
import com.link.riderhere.map.navi.NaviUtils.getFirstManeuver
import com.link.riderhere.map.navi.PositioningProvider
import com.link.riderhere.map.navi.PositioningSimulator
import com.link.riderhere.map.navi.VirtualNavigation
import com.link.riderhere.map.navi.VoiceAssistant
import com.link.riderhere.map.search.data.repository.SearchRepositoryImpl
import com.link.riderhere.map.search.domain.repository.SearchRepository
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.suspendCancellableCoroutine
import java.lang.ref.WeakReference
import kotlin.coroutines.resumeWithException

/**
 * 地图管理器：负责地图导航、路径规划、POI搜索等功能的集中管理
 * @author: w.feng
 * @date: 2025-05-06
 */
internal class MapManager {
    private val searchRepository: SearchRepository = SearchRepositoryImpl()
    private val locationRepository = LocationRepositoryImpl(HereLocationDataSourceImpl())
    private var isNavi = false
    private var isEmulator = false
    private var currentRoute: Route? = null
    private val mNaviCallbacks: MutableList<WeakReference<NaviCallback>> = mutableListOf()
    private val mNavigationInfo by lazy {
        NavigationInfo(
            naviType = 0,
            iconType = 0,
            curStepRetainDistance = 0,
            curStepRetainTime = 0,
            pathRetainDistance = 0,
            pathRetainTime = 0,
            currentRoadName = "",
            nextRoadName = "",
            routeRemainLightCount = 0,
            mapType = 1,
            turnIconName = "",
            turnKind = 0
        )
    }

    private val callback = object : NaviCallback() {
        override fun onArriveDestination() {
            super.onArriveDestination()
            notifyCallbacks {
                it.onArriveDestination()
            }
        }

        override fun onStopNavi() {
            super.onStopNavi()
            notifyCallbacks { it.onStopNavi() }
        }

        override fun onStartNavi() {
            super.onStartNavi()
            notifyCallbacks { it.onStartNavi() }
        }

        override fun onNaviDataChanged(navigationInfo: NavigationInfo) {
            super.onNaviDataChanged(navigationInfo)
            notifyCallbacks { it.onNaviDataChanged(navigationInfo) }
        }
    }

    /** 路径规划引擎 */
    private val routingEngine by lazy {
        try {
            RoutingEngine()
        } catch (e: InstantiationErrorException) {
            throw RuntimeException("Initialization of RoutingEngine failed: " + e.error.name)
        }
    }

    /** 定位服务提供者 */
    private val positioningProvider by lazy {
        PositioningProvider()
    }

    /** 本地模拟定位模拟器 */
    private val positioningLocalSimulator by lazy {
        PositioningSimulator()
    }

    /** 虚拟定位模拟器 */
    private val positioningVirtualSimulator by lazy {
        PositioningSimulator()
    }

    private val voiceAssistant = VoiceAssistant(RiderMap.instance.getApplication())

    /** 本地导航器 */
    private val localNavigation by lazy {
        LocalNavigation(
            RiderMap.instance.getApplication(),
            voiceAssistant,
            callback,
            positioningProvider,
            positioningLocalSimulator
        )
    }

    /** 虚拟导航器 */
    private val virtualNavigation by lazy {
        VirtualNavigation(
            RiderMap.instance.getApplication(),
            positioningProvider,
            positioningVirtualSimulator
        )
    }

    /**
     * 添加导航数据回调监听
     * @param callback 导航回调
     */
    @Synchronized
    fun addNaviDataCallback(callback: NaviCallback) {
        mNaviCallbacks.add(WeakReference(callback))
    }

    /**
     * 移除导航数据回调监听
     * @param callback 要移除的导航回调
     */
    @Synchronized
    fun removeNaviDataCallback(callback: NaviCallback) {
        mNaviCallbacks.removeIf { it.get() == callback }
    }

    /**
     * 通知所有回调
     * @param action 对每个回调执行的操作
     */
    private fun notifyCallbacks(action: (NaviCallback) -> Unit) {
        mNaviCallbacks.forEach { weakRef ->
            weakRef.get()?.let(action)
        }
    }


    /**
     * 释放资源
     */
    fun destroy() {
        //todo destroy
        mNaviCallbacks.clear()
        Log.i(TAG, "MapManager资源已释放")
    }


    @OptIn(ExperimentalCoroutinesApi::class)
    suspend fun calculateRoute(
        context: Context,
        from: GeoCoordinates,
        to: GeoCoordinates
    ): List<Route?> = suspendCancellableCoroutine { continuation ->
        try {
            val startWaypoint = Waypoint(from)
            val destinationWaypoint = Waypoint(to)
            val waypoints = listOf(startWaypoint, destinationWaypoint)
//            val carOptions = CarOptions().apply {
//                routeOptions.alternatives = 2
//                routeOptions.enableRouteHandle = true
//            }
            val scooterOptions = ScooterOptions().apply {
                routeOptions.alternatives = 2
                routeOptions.enableRouteHandle = true
            }
            val calculateRouteCallback = object : CalculateRouteCallback {
                override fun onRouteCalculated(
                    error: RoutingError?,
                    routes: List<Route>?
                ) {
                    when {
                        error != null -> {
                            Log.e(TAG, "路线计算失败: ${error.toString()}")
                            if (continuation.isActive) {
                                continuation.resumeWithException(Exception(error.toString()))
                            }
                        }

                        routes.isNullOrEmpty() -> {
                            Log.e(TAG, "路线计算返回为空")
                            if (continuation.isActive) {
                                continuation.resumeWithException(Exception("路线计算返回为空"))
                            }
                        }

                        else -> {
                            if (continuation.isActive) {
                                continuation.resume(routes) {
                                    Log.d(TAG, "路线计算已取消")
                                }
                            }
                        }
                    }
                }

            }
            routingEngine.calculateRoute(
                waypoints,
                scooterOptions,
                calculateRouteCallback
            )
        } catch (e: Exception) {
            Log.e(TAG, "路线计算过程中出错", e)
            if (continuation.isActive) {
                continuation.resumeWithException(e)
            }
        }
    }

    /**
     * 检查是否正在导航
     * @return 是否处于导航状态
     */
    fun isNavi(): Boolean = isNavi

    /**
     * 开始导航
     */
    fun startNavi() {
        isNavi = true
        notifyCallbacks { it.onStartNavi() }
        currentRoute?.let { route ->
            localNavigation.startNavigation(route, isEmulator)
            virtualNavigation.startNavigation(route, isEmulator)
        } ?: Log.e(TAG, "无法开始导航：当前未选择路线")
    }

    /**
     * 开始本地渲染
     * @param mapView 地图视图
     */
    internal fun startLocalRendering(mapView: MapView) {
        localNavigation.startRendering(mapView)
        notifyFirstManeuver()
    }

    /**
     * 开始虚拟渲染
     * @param mapView 地图视图
     */
    internal fun startVirtualRendering(mapView: MapView) {
        virtualNavigation.startRendering(mapView)
        notifyFirstManeuver()
    }

    private fun notifyFirstManeuver() {
        currentRoute?.let {
            val navigationInfo = getFirstManeuver(RiderMap.instance.getApplication(),it)
            notifyCallbacks { it.onNaviDataChanged(navigationInfo) }
        }
    }

    /**
     * 停止导航
     */
    fun stopNavi() {
        Log.d(TAG, "stopNavi")
        isNavi = false
        notifyCallbacks { it.onStopNavi() }
        // 停止导航及渲染
        localNavigation.apply {
            stopNavigation()
            stopRendering()
            stopLocationProvider()
        }

        virtualNavigation.apply {
            stopNavigation()
            stopRendering()
            stopLocationProvider()
        }
    }

    /**
     * 选择路线
     * @param route 要选择的路线
     */
    fun selectRoute(route: Route) {
        currentRoute = route
        Log.d(TAG, "已选择路线")
    }

    /**
     * 获取当前位置信息
     * @return 位置信息
     */
    fun getLbsLocation(): Location? {
        return locationRepository.getLbsLocation()
    }

    /**
     * 开始定位
     * @param listener 位置监听器
     */
    fun startLbsLocation(listener: HereLocationListener) {
        locationRepository.startLbsLocation(listener)
    }

    /**
     * 停止定位
     */
    fun stopLbsLocation() {
        locationRepository.stopLbsLocation()
    }

    /**
     * 销毁位置仓库，释放资源
     */
    fun destroyLocationRepository() {
        locationRepository.destroy()
    }

    /**
     * 设置导航类型
     * @param isEmulator 是否为模拟导航
     */
    fun setNaviType(isEmulator: Boolean) {
        this.isEmulator = isEmulator
        Log.d(TAG, "导航类型设置为: ${if (isEmulator) "模拟导航" else "实时导航"}")
    }

    /**
     * 地理编码搜索
     * @param context 上下文
     * @param geoCoordinates 经纬度
     * @return 位置信息或null
     */
    suspend fun geocodeSearch(
        context: Context,
        geoCoordinates: GeoCoordinates
    ): Place? {
        return searchRepository.geocodeSearch(context, geoCoordinates)
    }

    /**
     * 关键词搜索
     * @param context 上下文
     * @param keyWord 搜索关键词
     * @return 搜索结果
     */
    suspend fun search(context: Context, keyWord: String): SearchResult {
        locationRepository.getLbsLocation()?.let {
            return searchRepository.search(context, keyWord, it)
        } ?: return SearchResult(-1, null, null)
    }

    fun startCameraTracking() {
        localNavigation.startCameraTracking()
    }

    fun stopCameraTracking() {
        localNavigation.stopCameraTracking()
    }

    internal fun getRoute(): Route? {
        return currentRoute
    }


    companion object {
        private const val TAG = "NaviDataManager"
    }
}
