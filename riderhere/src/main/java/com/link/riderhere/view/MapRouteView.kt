package com.link.riderhere.view

import android.content.Context
import android.content.res.Resources
import android.util.AttributeSet
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import androidx.core.graphics.toColorInt
import androidx.core.os.ConfigurationCompat
import com.here.sdk.core.Anchor2D
import com.here.sdk.core.Color
import com.here.sdk.core.GeoBox
import com.here.sdk.core.GeoCoordinates
import com.here.sdk.core.GeoOrientationUpdate
import com.here.sdk.core.Point2D
import com.here.sdk.core.Rectangle2D
import com.here.sdk.core.Size2D
import com.here.sdk.gestures.GestureType
import com.here.sdk.gestures.TapListener
import com.here.sdk.mapview.LineCap
import com.here.sdk.mapview.MapImageFactory
import com.here.sdk.mapview.MapMarker
import com.here.sdk.mapview.MapMeasureDependentRenderSize
import com.here.sdk.mapview.MapPickResult
import com.here.sdk.mapview.MapPolyline
import com.here.sdk.mapview.MapScene
import com.here.sdk.mapview.MapScheme
import com.here.sdk.mapview.MapView
import com.here.sdk.mapview.MapViewBase
import com.here.sdk.mapview.RenderSize
import com.here.sdk.routing.Route
import com.link.riderhere.R
import com.link.riderhere.api.RiderMap
import com.link.riderhere.databinding.MapRouteViewBinding
import com.link.riderhere.utils.AutoSizeUtils
import java.util.Locale
import kotlin.collections.forEachIndexed

/**
 * 地图路径类，提供一个简单的实现
 */
class MapRouteView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyle: Int = 0
) : FrameLayout(context, attrs, defStyle) {
    private val binding = MapRouteViewBinding.inflate(LayoutInflater.from(context), this, true)
    private val mapView: MapView
    private var selectedRouteIndex = -1

    private val routeOverLays: HashMap<String, MapPolyline> = hashMapOf()

    private val routes = ArrayList<Route>()

    private var navigationView: NaviPortView? = null

    private var navigationContainer: FrameLayout? = null

    private var onStopNavigationListener: () -> Unit = {}

    private var isNight = false

    private var originMarker: MapMarker? = null
    private var destinationMarker: MapMarker? = null

    fun setNavi() {
        addNaviView(context)
    }


    fun initView() {
        AutoSizeUtils.updateResources(context)
    }

    /**
     * 设置导航按钮监听
     * @param listener () -> Unit
     */
    fun setNavigateButtonListener(listener: () -> Unit) {
        binding.btnNavigate.setOnClickListener {
            listener()
        }
    }

    /**
     * 设置停止导航按钮监听
     * @param listener () -> Unit
     */
    fun setStopNaviButtonListener(listener: () -> Unit) {
        onStopNavigationListener = listener
    }

    fun showExitBar() {
        navigationView?.showExitBar()
    }

    /**
     * 设置后退按钮监听
     * @param listener (Int) -> Unit
     */
    fun setBackButtonListener(listener: () -> Unit) {
        binding.llBack.setOnClickListener {
            listener()
        }
    }


    /**
     * 设置全屏导航按钮可用
     * @param isEnabled 是否可用
     */
    fun setNavigateEnable(isEnabled: Boolean) {
        binding.btnNavigate.isEnabled = isEnabled
    }

    /**
     * 开启导航动画
     */
    fun startNaviAnimation() {
        Log.d(TAG, "startNaviAnimation")
        binding.coverLayout.visibility = View.VISIBLE
        initView()
    }

    /**
     * 停止导航动画
     */
    fun stopNaviAnimation() {
        Log.d(TAG, "stopNaviAnimation")
        binding.coverLayout.visibility = View.GONE
    }

    /**
     * 设置起点
     * @param address 地址
     */
    fun setStart(address: String?) {
        binding.tvAddressStart.text = address
    }

    /**
     * 设置终点
     * @param address 地址
     */
    fun setDestination(address: String?) {
        binding.tvAddressEnd.text = address
    }


    fun setStartListener(listener: () -> Unit) {
        binding.tvAddressStart.setOnClickListener {
            listener()
        }
    }

    fun setEndListener(listener: () -> Unit) {
        binding.tvAddressEnd.setOnClickListener {
            listener()
        }
    }


    /**
     * 设置路径列表
     * @param routes 路径列表
     */
    fun setRouteList(from: GeoCoordinates, to: GeoCoordinates, routes: List<Route>) {
        originMarker = originMarker?.apply { coordinates = from }
            ?: addMapMarker(
                from,
                if (context.isChina()) R.drawable.navi_start else R.drawable.navi_start_eng
            )

        destinationMarker = destinationMarker?.apply { coordinates = to }
            ?: addMapMarker(
                to,
                if (context.isChina()) R.drawable.navi_end else R.drawable.navi_end_eng
            )
        updateRouteButton(routes)
        updateMapRouteView(routes)
    }

    private fun addMapMarker(geoCoordinates: GeoCoordinates, resourceId: Int): MapMarker {
        val mapImage = MapImageFactory.fromResource(context.resources, resourceId)
        val anchor2D = Anchor2D(0.5, 1.0)
        val mapMarker = MapMarker(geoCoordinates, mapImage, anchor2D)
        mapView.mapScene.addMapMarker(mapMarker)
        return mapMarker
    }


    private fun addNaviView(context: Context) {
        Log.d(TAG, "addNaviView")
        if (navigationView != null) return
        navigationView = NaviPortView(context) {
            onStopNavigationListener()
        }
        navigationContainer?.addView(navigationView)
    }

    private fun selectRoute(handle: String?, index: Int) {
        if (index !in 0..2 || selectedRouteIndex == index) return
        when (index) {
            0 -> binding.firstRoute.isSelected = true
            1 -> binding.secondRoute.isSelected = true
            2 -> binding.thirdRoute.isSelected = true
        }
        when (selectedRouteIndex) {
            0 -> binding.firstRoute.isSelected = false
            1 -> binding.secondRoute.isSelected = false
            2 -> binding.thirdRoute.isSelected = false
        }
        routes.getOrNull(selectedRouteIndex)?.routeHandle?.handle?.let {
            routeOverLays[it]?.let { poly -> updatePolylineColor(poly, false) }
        }
        routeOverLays[handle]?.let { updatePolylineColor(it, true) }
        selectedRouteIndex = index
    }

    private fun updatePolylineColor(polyline: MapPolyline, isSelected: Boolean) {
        val color = if (isSelected)
            Color(0f, 0f, 1f, 0.9f) // 蓝色
        else
            Color(0.5f, 0.5f, 0.5f, 0.7f) // 灰色
        try {
            polyline.setRepresentation(
                MapPolyline.SolidRepresentation(
                    MapMeasureDependentRenderSize(RenderSize.Unit.PIXELS, 20.0),
                    color,
                    LineCap.ROUND
                )
            )
            polyline.drawOrder = if (isSelected) 1 else 0
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }


    private fun updateMapRouteView(routes: List<Route>) {
        routeOverLays.values.forEach { runCatching { mapView.mapScene.removeMapPolyline(it) } }
        routeOverLays.clear()
        var routeCoordinates = mutableListOf<GeoCoordinates>()
        routes.forEachIndexed { i, route ->
            val geoPolyline = route.geometry
            routeCoordinates.addAll(geoPolyline.vertices)
            val widthInPixels = 20.0
            val lineColor = if (i == selectedRouteIndex)
                Color(0f, 0f, 1f, 0.9f) else Color(0.5f, 0.5f, 0.5f, 0.7f)
            try {
                val polyline = MapPolyline(
                    geoPolyline,
                    MapPolyline.SolidRepresentation(
                        MapMeasureDependentRenderSize(RenderSize.Unit.PIXELS, widthInPixels),
                        lineColor,
                        LineCap.ROUND
                    )
                )
                polyline.drawOrder = if (i == selectedRouteIndex) 1 else 0
                mapView.mapScene.addMapPolyline(polyline)
                this.routes.add(route)
                route.routeHandle?.handle?.let { handle ->
                    routeOverLays[handle] = polyline
                }
            } catch (e: Exception) {
                Log.e(TAG, "addMapPolyline error", e)
            }
        }
        if (routeCoordinates.isNotEmpty()) {
            getGeoBoxFromCoordinates(routeCoordinates)?.let { boundingBox ->
                val orientation = GeoOrientationUpdate(0.0, 0.0)
                val marginLeft = 100.0
                val marginRight = 100.0
                val marginTop = 350.0
                val marginBottom = 350.0
                val mapWidth = mapView.width.toDouble()
                val mapHeight = mapView.height.toDouble()
                if (mapWidth > marginLeft + marginRight && mapHeight > marginTop + marginBottom) {
                    val viewRect = Rectangle2D(
                        Point2D(marginLeft, marginTop),
                        Size2D(
                            mapWidth - marginLeft - marginRight,
                            mapHeight - marginTop - marginBottom
                        )
                    )
                    mapView.camera.lookAt(boundingBox, orientation, viewRect)
                } else {
                    mapView.camera.lookAt(boundingBox, orientation)
                }
            }
        }
    }

    private fun getGeoBoxFromCoordinates(coordinates: List<GeoCoordinates>): GeoBox? {
        if (coordinates.isEmpty()) return null
        var minLat = coordinates[0].latitude
        var maxLat = coordinates[0].latitude
        var minLon = coordinates[0].longitude
        var maxLon = coordinates[0].longitude
        for (coordinate in coordinates) {
            minLat = minOf(minLat, coordinate.latitude)
            maxLat = maxOf(maxLat, coordinate.latitude)
            minLon = minOf(minLon, coordinate.longitude)
            maxLon = maxOf(maxLon, coordinate.longitude)
        }
        val southWestCorner = GeoCoordinates(minLat, minLon)
        val northEastCorner = GeoCoordinates(maxLat, maxLon)
        return GeoBox(southWestCorner, northEastCorner)
    }

    private fun updateRouteButton(routes: List<Route>) {
        this.routes.clear()
        this.routes.addAll(routes)
        routes.forEachIndexed { index, route ->
            if (index > 2) {
                return@forEachIndexed
            }
            when (index) {
                0 -> {
                    selectedRouteIndex = 0
                    binding.firstRoute.setData(
                        context.getString(R.string.route_recommended),
                        route,
                        isNight
                    )
                    binding.firstRoute.isSelected = true
                    selectAndNotifyRoute(route.routeHandle?.handle, 0)
                }

                1 -> {
                    binding.secondRoute.visibility = View.VISIBLE
                    binding.secondRoute.setData(
                        context.getString(R.string.route_option_2),
                        route,
                        isNight
                    )
                }

                2 -> {
                    binding.thirdRoute.visibility = View.VISIBLE
                    binding.thirdRoute.setData(
                        context.getString(R.string.route_option_3),
                        route,
                        isNight
                    )
                }
            }
        }
    }

    override fun onFinishInflate() {
        super.onFinishInflate()
        mapView.onCreate(null)
        mapView.apply {
            gestures.apply {
                disableDefaultAction(GestureType.TWO_FINGER_TAP)
                disableDefaultAction(GestureType.DOUBLE_TAP)
                disableDefaultAction(GestureType.PINCH_ROTATE)
            }
            camera.zoomTo(15.0)
        }
    }

    override fun onVisibilityChanged(changedView: View, visibility: Int) {
        super.onVisibilityChanged(changedView, visibility)
        if (visibility == View.VISIBLE) {
            mapView.onResume()
        } else {
            mapView.onPause()
        }
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        mapView.onResume()
    }


    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        routeOverLays.forEach { runCatching { mapView.mapScene.removeMapPolyline(it.value) } }
        routeOverLays.clear()
        mapView.onDestroy()
        onStopNavigationListener = {}
    }

    init {
        AutoSizeUtils.updateResources(context)
        navigationContainer = binding.coverLayoutNew.naviContainer
        mapView = binding.map
        setupRouteButton(binding.firstRoute, 0)
        setupRouteButton(binding.secondRoute, 1)
        setupRouteButton(binding.thirdRoute, 2)
    }

    private fun setupRouteButton(routeView: View, index: Int) {
        routeView.setOnClickListener {
            if (routes.size > index) {
                val handle = routes[index].routeHandle?.handle
                if (!routeView.isSelected) {
                    selectAndNotifyRoute(handle, index)
                }
            }
        }
    }

    private fun changeMap() {
        val mapScheme = RiderMap.instance.getDefaultModeNavi()
        loadMap(mapScheme)
        navigationView?.loadMap(mapScheme)
    }

    private fun loadMap(mapScheme: MapScheme) {
        Log.d(TAG, "loadMap: $mapScheme")
        mapView.mapScene.loadScene(mapScheme) { mapError ->
            if (mapError != null) {
                Log.d(TAG, "Loading map failed: mapError: ${mapError.name}")
            } else {
                setTapListener()
            }
        }
    }

    private fun setTapListener() {
        mapView.gestures.tapListener = TapListener { tapCoordinates ->
            // 构造一个以点击点为中心的 50x50 像素矩形区域
            val tapOrigin = Point2D(tapCoordinates.x, tapCoordinates.y)
            val tapAreaSize = Size2D(50.0, 50.0)
            val tapRectangle = Rectangle2D(tapOrigin, tapAreaSize)
            // 只拾取地图上的 MapPolyline
            val contentTypesToPickFrom = listOf(MapScene.MapPickFilter.ContentType.MAP_ITEMS)
            val filter = MapScene.MapPickFilter(contentTypesToPickFrom)
            mapView.pick(filter, tapRectangle, object : MapViewBase.MapPickCallback {
                override fun onPickMap(pickResult: MapPickResult?) {
                    if (pickResult == null) return
                    val pickedItems = pickResult.mapItems
                    val pickedPolylines = pickedItems?.polylines ?: emptyList()
                    if (pickedPolylines.isEmpty()) return
                    val mapPolyline = pickedPolylines[0]
                    handlePickedMapPolyline(mapPolyline)
                }
            })
        }
    }

    private fun handlePickedMapPolyline(mapPolyline: MapPolyline) {
        val handle = routeOverLays.entries.find { it.value == mapPolyline }?.key
        val pickedRouteIndex = routes.indexOfFirst { it.routeHandle?.handle == handle }
        selectAndNotifyRoute(handle, pickedRouteIndex)
    }

    fun initTheme(
        themeResourceProvider: (resId: Int) -> Int,
        themedColorProvider: (day: String, night: String) -> String,
        isNightThemeActive: () -> Boolean
    ) {
        Log.d(TAG, "initTheme: Is night mode active? ${isNightThemeActive()}")
        binding.linearLayoutCompat.setBackgroundColor(
            themedColorProvider(
                "#FFFFFF",
                "#2A3042"
            ).toColorInt()
        )
        binding.btnNavigate.setTextColor(themedColorProvider("#5C7BD7", "#FFFFFF").toColorInt())
        binding.btnNavigate.setBackgroundResource(themeResourceProvider(R.drawable.bt_map_navi_background))
        binding.seBackground.setBackgroundResource(themeResourceProvider(R.drawable.start_end_background))
        binding.routeBottomBox.setBackgroundResource(themeResourceProvider(R.drawable.route_select_background))
        binding.ibBack.setBackgroundResource(themeResourceProvider(R.drawable.btn_map_back))

        //路径规划

        binding.firstRoute.onThemeChange(isNightThemeActive())
        binding.secondRoute.onThemeChange(isNightThemeActive())
        binding.thirdRoute.onThemeChange(isNightThemeActive())
        isNight = isNightThemeActive()
        navigationView?.initTheme(themeResourceProvider, themedColorProvider, isNightThemeActive)
        changeMap()
    }

    private fun selectAndNotifyRoute(handle: String?, index: Int) {
        selectRoute(handle, index)
        if (index in 0..2 && routes.size > index) {
            RiderMap.instance.selectRoute(routes[index])
        }
    }

    companion object {
        fun Context.isChina(): Boolean {
            // 1. 使用兼容库获取当前首选的 Locale 对象
            val currentLocale: Locale? = ConfigurationCompat.getLocales(Resources.getSystem().configuration)[0]

            // 2. 分别检查语言和国家代码
            // locale.language 会返回 "zh"
            // locale.country 会返回 "CN"
            return currentLocale != null &&
                    currentLocale.language == "zh" &&
                    currentLocale.country == "CN"
        }

        private const val TAG = "MapRouteView"
    }
}